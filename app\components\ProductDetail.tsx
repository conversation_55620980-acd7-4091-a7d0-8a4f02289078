"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { ArrowR<PERSON>, CheckCircle } from "lucide-react"
import type { Product, Package } from "../types"
import { convertAndFormatPrice } from "../utils/currency"

interface ProductDetailProps {
  product: Product
  onBack: () => void
  onPackageSelect: (pkg: Package) => void
  selectedPackage?: Package | null
}

export default function ProductDetail({ 
  product, 
  onBack, 
  onPackageSelect, 
  selectedPackage 
}: ProductDetailProps) {
  const [activePackage, setActivePackage] = useState<Package | null>(null)

  // Auto-select first package on load
  useEffect(() => {
    if (product.packages.length > 0 && !selectedPackage) {
      const firstPackage = product.packages[0]
      setActivePackage(firstPackage)
      onPackageSelect(firstPackage)
    } else if (selectedPackage) {
      setActivePackage(selectedPackage)
    }
  }, [product.packages, selectedPackage, onPackageSelect])

  const handlePackageClick = (pkg: Package) => {
    setActivePackage(pkg)
    onPackageSelect(pkg)
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header with Breadcrumb */}
      <div className="sticky top-0 z-10 bg-gray-900/95 backdrop-blur-md border-b border-gray-800/50">
        <div className="container mx-auto px-4 py-3">
          {/* Breadcrumb Navigation */}
          <nav className="flex items-center space-x-2 space-x-reverse text-sm text-gray-400 mb-3 overflow-x-auto">
            <Link href="/" className="hover:text-purple-400 transition-colors whitespace-nowrap">
              الرئيسية
            </Link>
            <ArrowRight className="w-4 h-4 flex-shrink-0" />
            <Link href="/shop" className="hover:text-purple-400 transition-colors whitespace-nowrap">
              المتجر
            </Link>
            {product.categoryData ? (
              <>
                <ArrowRight className="w-4 h-4 flex-shrink-0" />
                <Link
                  href={`/category/${product.categoryData.slug}`}
                  className="hover:text-purple-400 transition-colors whitespace-nowrap"
                >
                  {product.categoryData.name || product.categoryData.slug}
                </Link>
              </>
            ) : product.category ? (
              <>
                <ArrowRight className="w-4 h-4 flex-shrink-0" />
                <span className="text-gray-500 whitespace-nowrap">{product.category}</span>
              </>
            ) : null}
            <ArrowRight className="w-4 h-4 flex-shrink-0" />
            <span className="text-white whitespace-nowrap truncate max-w-[150px]">{product.title}</span>
          </nav>


        </div>
      </div>

      {/* Package Selection Grid */}
      <div className="container mx-auto px-4 pt-4 pb-24">
        <div className="grid grid-cols-2 gap-3">
          {product.packages.map((pkg) => {
            const isSelected = activePackage?.id === pkg.id
            const hasDiscount = pkg.discount && pkg.discount > 0
            
            return (
              <button
                key={pkg.id}
                onClick={() => handlePackageClick(pkg)}
                className={`relative p-3 rounded-xl border-2 transition-all duration-200 text-right ${
                  isSelected
                    ? "border-purple-500 bg-purple-500/10 shadow-lg shadow-purple-500/20"
                    : "border-gray-700/50 bg-gray-800/30 hover:border-gray-600 hover:bg-gray-800/50"
                }`}
              >
                {/* Discount Badge */}
                {hasDiscount && (
                  <div className="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-2 py-1 rounded-full z-10">
                    -{pkg.discount}%
                  </div>
                )}

                {/* Selected Indicator */}
                {isSelected && (
                  <div className="absolute top-2 left-2 z-10">
                    <CheckCircle className="w-5 h-5 text-purple-400 fill-current" />
                  </div>
                )}

                <div className="space-y-3">
                  {/* Package Image */}
                  <div className="w-full h-20 rounded-lg overflow-hidden bg-gray-700/50 flex items-center justify-center">
                    <Image
                      src={pkg.image || product.coverImage}
                      alt={pkg.name}
                      width={80}
                      height={80}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Package Info */}
                  <div className="space-y-2">
                    <h3 className="font-bold text-white text-sm leading-tight">{pkg.name}</h3>

                    <div className="space-y-1">
                      <div className="text-xl font-bold text-purple-400">
                        {convertAndFormatPrice(pkg.price)}
                      </div>
                      {hasDiscount && pkg.originalPrice && (
                        <div className="text-xs text-gray-400 line-through">
                          {convertAndFormatPrice(pkg.originalPrice)}
                        </div>
                      )}
                    </div>

                    {pkg.description && (
                      <p className="text-xs text-gray-400 leading-tight">{pkg.description}</p>
                    )}

                    {/* Stock Info for Digital Codes */}
                    {pkg.hasDigitalCodes && (
                      <div className="text-xs">
                        {pkg.availableCodesCount && pkg.availableCodesCount > 0 ? (
                          <span className="text-green-400">
                            متوفر ({pkg.availableCodesCount} كود)
                          </span>
                        ) : (
                          <span className="text-red-400">نفد المخزون</span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </button>
            )
          })}
        </div>
      </div>
    </div>
  )
}
