"use client"

import { useState } from "react"
import CategorySection from "./components/CategorySection"
import PromoBanner from "./components/PromoBanner"
import PaginatedProductSection from "./components/PaginatedProductSection"
import AdminSetup from "./components/AdminSetup"
import TenantSwitcher from "./components/TenantSwitcher"
import TenantCreator from "./components/TenantCreator"
import { useData } from "./contexts/DataContext"

export default function HomePage() {
  // Use centralized data context
  const { products, homepageSections, currentUser } = useData()
  const userRole = currentUser?.role || "user"

  // Get active homepage sections sorted by order
  const activeSections = homepageSections.filter((section) => section.active).sort((a, b) => a.order - b.order)

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Promotional Banner */}
      <section className="container mx-auto px-4 pt-4 pb-2">
        <PromoBanner />
      </section>

      {/* Main Product Catalog */}
      <div className="container mx-auto px-4 py-4">
        <PaginatedProductSection
          title="🎮 جميع المنتجات"
          products={products}
          userRole={userRole}
          productsPerPage={20}
        />
      </div>

      {/* Featured Sections (Optional - can be removed if not needed) */}
      <div className="container mx-auto px-4 pb-8">
        {activeSections.slice(0, 2).map((section) => {
          const sectionProducts = products.filter((product) => section.productIds.includes(product.id))

          if (sectionProducts.length === 0) return null

          return (
            <CategorySection
              key={section.id}
              title={`${section.emoji || ""} ${section.title}`.trim()}
              products={sectionProducts.slice(0, 8)} // Limit to 8 products for featured sections
              userRole={userRole}
            />
          )
        })}
      </div>

      {/* Development Tools (Development Only) */}
      <AdminSetup />
      <TenantSwitcher />
      <TenantCreator />
    </div>
  )
}
