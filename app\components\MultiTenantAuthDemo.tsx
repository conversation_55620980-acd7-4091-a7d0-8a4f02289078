"use client"

import { useState } from "react"
import { useTenant } from "../contexts/TenantContext"
import { signUpWithEmail, signInWithEmail } from "../lib/supabase"

export default function MultiTenantAuthDemo() {
  const { tenant } = useTenant()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [name, setName] = useState("")
  const [isSignUp, setIsSignUp] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState("")

  // Only show this component in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!tenant) {
      setMessage("❌ لم يتم تحديد المتجر")
      return
    }

    setIsLoading(true)
    setMessage("⏳ جاري المعالجة...")

    try {
      if (isSignUp) {
        const result = await signUpWithEmail(email, password, name, tenant.id)
        if (result.success) {
          setMessage(`✅ تم إنشاء الحساب بنجاح في ${tenant.name}!`)
        } else {
          setMessage(`❌ فشل في إنشاء الحساب: ${result.error}`)
        }
      } else {
        const result = await signInWithEmail(email, password, tenant.id)
        if (result.success) {
          setMessage(`✅ تم تسجيل الدخول بنجاح في ${tenant.name}!`)
          // Refresh page to update auth state
          setTimeout(() => window.location.reload(), 1000)
        } else {
          setMessage(`❌ فشل في تسجيل الدخول: ${result.error}`)
        }
      }
    } catch (error) {
      setMessage("❌ حدث خطأ غير متوقع")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="fixed top-4 left-4 bg-gray-800 border border-gray-700 rounded-lg p-4 shadow-lg z-50 w-80">
      <h3 className="text-white font-bold mb-3">🔐 اختبار المصادقة متعددة المتاجر</h3>
      
      <div className="mb-3 p-2 bg-gray-700 rounded text-sm">
        <div className="text-gray-300">المتجر الحالي:</div>
        <div className="text-purple-400 font-medium">{tenant?.name || 'غير محدد'}</div>
        <div className="text-gray-400 text-xs">ID: {tenant?.slug || 'غير محدد'}</div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-3">
        <div className="flex gap-2">
          <button
            type="button"
            onClick={() => setIsSignUp(true)}
            className={`flex-1 px-3 py-2 rounded text-sm font-medium transition-colors ${
              isSignUp 
                ? 'bg-purple-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            إنشاء حساب
          </button>
          <button
            type="button"
            onClick={() => setIsSignUp(false)}
            className={`flex-1 px-3 py-2 rounded text-sm font-medium transition-colors ${
              !isSignUp 
                ? 'bg-purple-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            تسجيل دخول
          </button>
        </div>

        {isSignUp && (
          <input
            type="text"
            placeholder="الاسم"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
            required
          />
        )}

        <input
          type="email"
          placeholder="البريد الإلكتروني"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
          required
        />

        <input
          type="password"
          placeholder="كلمة المرور"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
          required
        />

        <button
          type="submit"
          disabled={isLoading || !tenant}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
        >
          {isLoading ? "⏳ جاري المعالجة..." : (isSignUp ? "إنشاء حساب" : "تسجيل دخول")}
        </button>
      </form>

      {message && (
        <div className="mt-3 p-2 bg-gray-700 rounded text-sm text-white">
          {message}
        </div>
      )}

      <div className="mt-3 p-2 bg-gray-700 rounded text-xs text-gray-300">
        💡 <strong>اختبار العزل:</strong>
        <br />1. أنشئ حساب بنفس البريد في متاجر مختلفة
        <br />2. تحقق من أن كل متجر له حساب منفصل
        <br />3. لا يمكن الدخول بحساب متجر في متجر آخر
      </div>
    </div>
  )
}
