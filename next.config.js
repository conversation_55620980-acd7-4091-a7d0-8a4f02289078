/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configure external image domains
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.jsdelivr.net',
        port: '',
        pathname: '/**',
      }
    ],
  },

  // Allow cross-origin requests for subdomain development
  async headers() {
    return [
      {
        source: '/_next/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
        ],
      },
    ]
  },

  // Configure allowed development origins for subdomains
  experimental: {
    allowedDevOrigins: [
      'store2.localhost:3001',
      'store3.localhost:3001',
      'localhost:3001'
    ],
    optimizeCss: false, // Disable CSS optimization in dev for faster builds
  },

}

module.exports = nextConfig
