/** @type {import('next').NextConfig} */
const nextConfig = {
  // Allow cross-origin requests for subdomain development
  async headers() {
    return [
      {
        source: '/_next/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
        ],
      },
    ]
  },
  
  // Configure allowed development origins for subdomains
  allowedDevOrigins: [
    'store2.localhost:3001',
    'store3.localhost:3001',
    'localhost:3001'
  ],
  
  // Optimize for development
  experimental: {
    optimizeCss: false, // Disable CSS optimization in dev for faster builds
  }
}

module.exports = nextConfig
