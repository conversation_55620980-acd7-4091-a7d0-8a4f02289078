import { supabase } from './supabase'
import { validateData, sanitizeObject } from './validations'
import { z } from 'zod'

// Category type definition
export interface Category {
  id: string
  tenant_id: string
  name: string
  slug: string
  description?: string
  icon?: string
  color: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
  product_count?: number // Virtual field for UI display
}

// Category validation schema
export const categorySchema = z.object({
  id: z.string().optional(),
  tenant_id: z.string().uuid('معرف المستأجر غير صالح').optional(),
  name: z.string()
    .min(1, 'اسم الفئة مطلوب')
    .max(100, 'اسم الفئة لا يمكن أن يزيد عن 100 حرف')
    .regex(/^[a-zA-Z0-9\u0600-\u06FF\s\-_]+$/, 'اسم الفئة يحتوي على أحرف غير مسموحة'),
  slug: z.string()
    .min(1, 'الرابط المختصر مطلوب')
    .max(100, 'الرابط المختصر لا يمكن أن يزيد عن 100 حرف')
    .regex(/^[a-z0-9\-_]+$/, 'الرابط المختصر يجب أن يحتوي على أحرف صغيرة وأرقام وشرطات فقط'),
  description: z.string()
    .max(500, 'الوصف لا يمكن أن يزيد عن 500 حرف')
    .optional(),
  icon: z.string()
    .max(10, 'الأيقونة لا يمكن أن تزيد عن 10 أحرف')
    .optional(),
  color: z.string()
    .regex(/^#[0-9a-fA-F]{6}$/, 'اللون يجب أن يكون بصيغة hex صالحة')
    .default('#6366f1'),
  sort_order: z.number()
    .int('ترتيب الفرز يجب أن يكون رقم صحيح')
    .min(0, 'ترتيب الفرز لا يمكن أن يكون سالب')
    .default(0),
  is_active: z.boolean().default(true)
})

export const categoryCreateSchema = categorySchema.omit({ id: true, tenant_id: true })
export const categoryUpdateSchema = categorySchema.partial().omit({ id: true, tenant_id: true })

// Helper function to generate slug from name
export function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .trim()
    // Replace Arabic characters with transliteration
    .replace(/[أإآا]/g, 'a')
    .replace(/[ب]/g, 'b')
    .replace(/[ت]/g, 't')
    .replace(/[ث]/g, 'th')
    .replace(/[ج]/g, 'j')
    .replace(/[ح]/g, 'h')
    .replace(/[خ]/g, 'kh')
    .replace(/[د]/g, 'd')
    .replace(/[ذ]/g, 'dh')
    .replace(/[ر]/g, 'r')
    .replace(/[ز]/g, 'z')
    .replace(/[س]/g, 's')
    .replace(/[ش]/g, 'sh')
    .replace(/[ص]/g, 's')
    .replace(/[ض]/g, 'd')
    .replace(/[ط]/g, 't')
    .replace(/[ظ]/g, 'z')
    .replace(/[ع]/g, 'a')
    .replace(/[غ]/g, 'gh')
    .replace(/[ف]/g, 'f')
    .replace(/[ق]/g, 'q')
    .replace(/[ك]/g, 'k')
    .replace(/[ل]/g, 'l')
    .replace(/[م]/g, 'm')
    .replace(/[ن]/g, 'n')
    .replace(/[ه]/g, 'h')
    .replace(/[و]/g, 'w')
    .replace(/[ي]/g, 'y')
    // Replace spaces and special characters with hyphens
    .replace(/[\s\u0600-\u06FF]+/g, '-')
    .replace(/[^a-z0-9\-]/g, '')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
}

// Get all categories for a tenant
export async function getCategories(tenantId: string): Promise<{ success: boolean; data?: Category[]; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select(`
        *,
        product_count:products(count)
      `)
      .eq('tenant_id', tenantId)
      .eq('is_active', true)
      .order('sort_order', { ascending: true })
      .order('name', { ascending: true })

    if (error) {
      console.error('Error fetching categories:', error)
      return { success: false, error: error.message }
    }

    // Transform the data to include product count
    const categories = data?.map(cat => ({
      ...cat,
      product_count: cat.product_count?.[0]?.count || 0
    })) || []

    return { success: true, data: categories }
  } catch (error) {
    console.error('Error in getCategories:', error)
    return { success: false, error: 'حدث خطأ في جلب الفئات' }
  }
}

// Get single category by ID
export async function getCategory(id: string, tenantId: string): Promise<{ success: boolean; data?: Category; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .single()

    if (error) {
      console.error('Error fetching category:', error)
      return { success: false, error: error.message }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error in getCategory:', error)
    return { success: false, error: 'حدث خطأ في جلب الفئة' }
  }
}

// Create new category
export async function createCategory(
  categoryData: z.infer<typeof categoryCreateSchema>, 
  tenantId: string
): Promise<{ success: boolean; data?: Category; error?: string }> {
  try {
    // Sanitize and validate input
    const sanitizedData = sanitizeObject(categoryData)
    const validation = validateData(categoryCreateSchema, sanitizedData)
    
    if (!validation.success) {
      return { success: false, error: validation.errors.join(', ') }
    }

    const validatedData = validation.data

    // Auto-generate slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = generateSlug(validatedData.name)
    }

    // Check for duplicate name in tenant
    const { data: existingByName } = await supabase
      .from('categories')
      .select('id')
      .eq('tenant_id', tenantId)
      .eq('name', validatedData.name)
      .single()

    if (existingByName) {
      return { success: false, error: 'اسم الفئة موجود بالفعل' }
    }

    // Check for duplicate slug in tenant
    const { data: existingBySlug } = await supabase
      .from('categories')
      .select('id')
      .eq('tenant_id', tenantId)
      .eq('slug', validatedData.slug)
      .single()

    if (existingBySlug) {
      return { success: false, error: 'الرابط المختصر موجود بالفعل' }
    }

    // Insert new category
    const { data, error } = await supabase
      .from('categories')
      .insert({
        ...validatedData,
        tenant_id: tenantId
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating category:', error)
      return { success: false, error: error.message }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error in createCategory:', error)
    return { success: false, error: 'حدث خطأ في إنشاء الفئة' }
  }
}

// Update existing category
export async function updateCategory(
  id: string,
  categoryData: z.infer<typeof categoryUpdateSchema>,
  tenantId: string
): Promise<{ success: boolean; data?: Category; error?: string }> {
  try {
    // Sanitize and validate input
    const sanitizedData = sanitizeObject(categoryData)
    const validation = validateData(categoryUpdateSchema, sanitizedData)
    
    if (!validation.success) {
      return { success: false, error: validation.errors.join(', ') }
    }

    const validatedData = validation.data

    // Auto-generate slug if name is being updated
    if (validatedData.name && !validatedData.slug) {
      validatedData.slug = generateSlug(validatedData.name)
    }

    // Check for duplicate name in tenant (excluding current category)
    if (validatedData.name) {
      const { data: existingByName } = await supabase
        .from('categories')
        .select('id')
        .eq('tenant_id', tenantId)
        .eq('name', validatedData.name)
        .neq('id', id)
        .single()

      if (existingByName) {
        return { success: false, error: 'اسم الفئة موجود بالفعل' }
      }
    }

    // Check for duplicate slug in tenant (excluding current category)
    if (validatedData.slug) {
      const { data: existingBySlug } = await supabase
        .from('categories')
        .select('id')
        .eq('tenant_id', tenantId)
        .eq('slug', validatedData.slug)
        .neq('id', id)
        .single()

      if (existingBySlug) {
        return { success: false, error: 'الرابط المختصر موجود بالفعل' }
      }
    }

    // Update category
    const { data, error } = await supabase
      .from('categories')
      .update(validatedData)
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .select()
      .single()

    if (error) {
      console.error('Error updating category:', error)
      return { success: false, error: error.message }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error in updateCategory:', error)
    return { success: false, error: 'حدث خطأ في تحديث الفئة' }
  }
}

// Delete category
export async function deleteCategory(id: string, tenantId: string): Promise<{ success: boolean; error?: string }> {
  try {
    // Check if category has products
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id')
      .eq('category_id', id)
      .eq('tenant_id', tenantId)
      .limit(1)

    if (productsError) {
      console.error('Error checking products:', productsError)
      return { success: false, error: 'حدث خطأ في التحقق من المنتجات' }
    }

    if (products && products.length > 0) {
      return { success: false, error: 'لا يمكن حذف الفئة لأنها تحتوي على منتجات' }
    }

    // Delete category
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id)
      .eq('tenant_id', tenantId)

    if (error) {
      console.error('Error deleting category:', error)
      return { success: false, error: error.message }
    }

    return { success: true }
  } catch (error) {
    console.error('Error in deleteCategory:', error)
    return { success: false, error: 'حدث خطأ في حذف الفئة' }
  }
}

// Reorder categories
export async function reorderCategories(
  categoryIds: string[], 
  tenantId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Update sort_order for each category
    const updates = categoryIds.map((id, index) => 
      supabase
        .from('categories')
        .update({ sort_order: index })
        .eq('id', id)
        .eq('tenant_id', tenantId)
    )

    const results = await Promise.all(updates)
    
    // Check if any update failed
    const failedUpdate = results.find(result => result.error)
    if (failedUpdate) {
      console.error('Error reordering categories:', failedUpdate.error)
      return { success: false, error: failedUpdate.error.message }
    }

    return { success: true }
  } catch (error) {
    console.error('Error in reorderCategories:', error)
    return { success: false, error: 'حدث خطأ في إعادة ترتيب الفئات' }
  }
}
