"use client"

import { useAuth } from '../contexts/AuthContext'
import { useTenant } from '../contexts/TenantContext'
import { useEffect, useState } from 'react'

export default function AuthDebugger() {
  const { authState, login, logout } = useAuth()
  const { tenant } = useTenant()
  const [debugInfo, setDebugInfo] = useState<string[]>([])
  const [performanceStats, setPerformanceStats] = useState({
    authStateChanges: 0,
    dbQueries: 0,
    cacheHits: 0,
    lastUpdate: Date.now()
  })

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => [...prev.slice(-9), `${new Date().toLocaleTimeString()}: ${info}`])
  }

  useEffect(() => {
    addDebugInfo(`Tenant: ${tenant?.name || 'None'} (${tenant?.id || 'No ID'})`)
  }, [tenant])

  useEffect(() => {
    addDebugInfo(`Auth State - User: ${authState.user?.email || 'None'}, Loading: ${authState.isLoading}, Authenticated: ${authState.isAuthenticated}`)

    // Track performance metrics
    setPerformanceStats(prev => ({
      ...prev,
      authStateChanges: prev.authStateChanges + 1,
      lastUpdate: Date.now()
    }))
  }, [authState])

  const testLogin = async () => {
    try {
      addDebugInfo('Testing <NAME_EMAIL>...')
      await login({ email: '<EMAIL>', password: '123456' })
      addDebugInfo('Login successful!')
    } catch (error) {
      addDebugInfo(`Login failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const testLogout = async () => {
    try {
      addDebugInfo('Testing logout...')
      await logout()
      addDebugInfo('Logout successful!')
    } catch (error) {
      addDebugInfo(`Logout failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/90 text-white p-4 rounded-lg max-w-md text-xs z-50">
      <h3 className="font-bold mb-2">🐛 Auth Debugger</h3>

      {/* Current Status */}
      <div className="mb-3 p-2 bg-gray-800 rounded">
        <div className="text-yellow-400">Current Status:</div>
        <div>User: {authState.user?.email || 'None'}</div>
        <div>Name: {authState.user?.name || 'None'}</div>
        <div>Authenticated: {authState.isAuthenticated ? '✅' : '❌'}</div>
        <div>Loading: {authState.isLoading ? '⏳' : '✅'}</div>
        <div>Tenant: {tenant?.name || 'None'}</div>
      </div>

      {/* Performance Metrics */}
      <div className="mb-3 p-2 bg-blue-900/50 rounded">
        <div className="text-blue-400">Performance:</div>
        <div>Auth Changes: {performanceStats.authStateChanges}</div>
        <div>DB Queries: {performanceStats.dbQueries}</div>
        <div>Cache Hits: {performanceStats.cacheHits}</div>
      </div>

      <div className="space-y-1 mb-3 max-h-32 overflow-y-auto">
        {debugInfo.map((info, index) => (
          <div key={index} className="text-green-400">{info}</div>
        ))}
      </div>

      <div className="space-x-2 space-x-reverse">
        <button
          onClick={testLogin}
          className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-white text-xs"
          disabled={authState.isLoading}
        >
          {authState.isLoading ? 'Testing...' : 'Login'}
        </button>

        <button
          onClick={testLogout}
          className="bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-white text-xs"
          disabled={authState.isLoading}
        >
          Logout
        </button>
      </div>
    </div>
  )
}
