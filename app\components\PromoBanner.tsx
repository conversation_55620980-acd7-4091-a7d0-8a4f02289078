"use client"

import Image from "next/image"
import Link from "next/link"
import { useState, useEffect, useRef, useCallback } from "react"
import { useData } from "../contexts/DataContext"
import type { BannerSlide } from "../types"

export default function PromoBanner() {
  const { banners } = useData()

  // ALL HOOKS MUST BE CALLED FIRST - BEFORE ANY CONDITIONAL LOGIC
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [currentX, setCurrentX] = useState(0)
  const [translateX, setTranslateX] = useState(0)
  const [dragProgress, setDragProgress] = useState(0) // -1 to 1, indicates drag direction and intensity
  const sliderRef = useRef<HTMLDivElement>(null)
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Get active banners sorted by order
  const activeBanners = banners.filter((banner) => banner.active).sort((a, b) => a.order - b.order)
  const bannerImages = activeBanners.map(banner => banner.image)
  const bannerCount = bannerImages.length

  // ALL useCallback and useEffect hooks MUST be here before any conditional returns
  const startAutoPlay = useCallback(() => {
    if (autoPlayRef.current) clearInterval(autoPlayRef.current)
    if (bannerCount > 0) {
      autoPlayRef.current = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % bannerCount)
      }, 4000)
    }
  }, [bannerCount])

  const stopAutoPlay = useCallback(() => {
    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current)
      autoPlayRef.current = null
    }
  }, [])

  useEffect(() => {
    if (isAutoPlaying) {
      startAutoPlay()
    } else {
      stopAutoPlay()
    }
    return () => stopAutoPlay()
  }, [isAutoPlaying, startAutoPlay, stopAutoPlay])

  const nextSlide = useCallback(() => {
    if (bannerCount > 0) {
      setCurrentSlide((prev) => (prev + 1) % bannerCount)
    }
  }, [bannerCount])

  const prevSlide = useCallback(() => {
    if (bannerCount > 0) {
      setCurrentSlide((prev) => (prev - 1 + bannerCount) % bannerCount)
    }
  }, [bannerCount])

  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index)
  }, [])

  // Touch and mouse event handlers
  const handleStart = useCallback((clientX: number) => {
    setIsDragging(true)
    setStartX(clientX)
    setCurrentX(clientX)
    setIsAutoPlaying(false)
  }, [])

  const handleMove = useCallback((clientX: number) => {
    if (!isDragging || !containerRef.current) return

    setCurrentX(clientX)
    const diff = clientX - startX
    const containerWidth = containerRef.current.offsetWidth
    const maxDrag = containerWidth * 0.3 // Limit drag to 30% of container width

    // Clamp the translation
    const clampedDiff = Math.max(-maxDrag, Math.min(maxDrag, diff))
    setTranslateX(clampedDiff)

    // Calculate drag progress (-1 to 1)
    setDragProgress(clampedDiff / maxDrag)
  }, [isDragging, startX])

  const handleEnd = useCallback(() => {
    if (!isDragging) return

    setIsDragging(false)
    const diff = currentX - startX
    const threshold = 50 // Minimum distance to trigger slide change

    if (Math.abs(diff) > threshold) {
      if (diff > 0) {
        prevSlide()
      } else {
        nextSlide()
      }
    }

    // Reset states
    setTranslateX(0)
    setDragProgress(0)
    setIsAutoPlaying(true)
  }, [isDragging, currentX, startX, nextSlide, prevSlide])

  // Mouse events
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    handleStart(e.clientX)
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    handleMove(e.clientX)
  }

  const handleMouseUp = () => {
    handleEnd()
  }

  const handleMouseLeave = () => {
    if (isDragging) {
      handleEnd()
    }
  }

  // Touch events
  const handleTouchStart = (e: React.TouchEvent) => {
    e.preventDefault()
    handleStart(e.touches[0].clientX)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    e.preventDefault()
    handleMove(e.touches[0].clientX)
  }

  const handleTouchEnd = (e: React.TouchEvent) => {
    e.preventDefault()
    handleEnd()
  }

  // Pause auto-play on hover
  const handleMouseEnter = () => {
    setIsAutoPlaying(false)
  }

  const handleMouseLeaveContainer = () => {
    if (!isDragging) {
      setIsAutoPlaying(true)
    }
  }

  // Helper function to get banner link (defined after all hooks)
  const getBannerLink = (banner: BannerSlide) => {
    switch (banner.linkType) {
      case "product":
        return `/product/${banner.linkValue}`
      case "collection":
        return `/collection/${banner.linkValue}`
      case "custom":
        return banner.linkValue || "#"
      default:
        return "#"
    }
  }

  // Don't render component if no banners available (AFTER all hooks)
  if (activeBanners.length === 0) {
    return null
  }

  return (
    <div
      ref={containerRef}
      className="relative overflow-hidden rounded-xl shadow-2xl aspect-[2.33/1] cursor-grab active:cursor-grabbing select-none"
      style={{ touchAction: 'pan-x' }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeaveContainer}
    >
      {/* Image Slider */}
      <div
        ref={sliderRef}
        className="relative w-full h-full"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {bannerImages.map((image, index) => {
          const isActive = index === currentSlide
          const isPrev = index === (currentSlide - 1 + bannerImages.length) % bannerImages.length
          const isNext = index === (currentSlide + 1) % bannerImages.length
          const currentBanner = activeBanners[index] // Get banner data if available

          let opacity = 0
          let transform = 'translateX(0px)'

          if (isActive) {
            opacity = 1
            if (isDragging) {
              transform = `translateX(${translateX}px)`
            }
          } else if (isDragging) {
            if (isPrev && dragProgress > 0) {
              opacity = Math.min(0.7, dragProgress * 2)
              transform = `translateX(${translateX - (containerRef.current?.offsetWidth || 0)}px)`
            } else if (isNext && dragProgress < 0) {
              opacity = Math.min(0.7, Math.abs(dragProgress) * 2)
              transform = `translateX(${translateX + (containerRef.current?.offsetWidth || 0)}px)`
            }
          }

          const bannerContent = (
            <div
              key={index}
              className="absolute inset-0"
              style={{
                opacity,
                transform,
                transition: isDragging ? 'none' : 'opacity 0.7s ease-in-out, transform 0.3s ease-out'
              }}
            >
              <Image
                src={image}
                alt={currentBanner?.title || `بانر ${index + 1}`}
                fill
                className="object-cover pointer-events-none"
                priority={index === 0}
                quality={90}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 100vw, 100vw"
              />

              {/* Overlay content for database banners */}
              {currentBanner && (currentBanner.title || currentBanner.subtitle) && (
                <>
                  <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/30 to-transparent" />
                  <div className="absolute inset-0 flex items-center">
                    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                      <div className="max-w-lg">
                        {currentBanner.title && (
                          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-2 sm:mb-4 text-white leading-tight">
                            {currentBanner.title}
                          </h2>
                        )}
                        {currentBanner.subtitle && (
                          <p className="text-sm sm:text-lg md:text-xl text-gray-200 mb-4 sm:mb-6 leading-relaxed">
                            {currentBanner.subtitle}
                          </p>
                        )}
                        {currentBanner.linkType !== "none" && (
                          <Link
                            href={getBannerLink(currentBanner)}
                            className="inline-block btn-primary text-sm sm:text-lg px-4 sm:px-8 py-2 sm:py-3 shadow-xl hover:shadow-purple-500/25 transition-all duration-300"
                          >
                            تسوق الآن
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          )

          // Wrap with link if banner has link and no overlay content
          if (currentBanner && currentBanner.linkType !== "none" && !currentBanner.title && !currentBanner.subtitle) {
            return (
              <Link key={index} href={getBannerLink(currentBanner)} className="block">
                {bannerContent}
              </Link>
            )
          }

          return bannerContent
        })}





        {/* Enhanced Dots Indicator - Smaller and More Subtle */}
        <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex items-center space-x-1.5 bg-black/20 backdrop-blur-sm rounded-full px-2 py-1">
          {bannerImages.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`relative transition-all duration-300 ease-out focus:outline-none focus:ring-1 focus:ring-white/30 rounded-full ${
                index === currentSlide
                  ? "w-4 h-2 bg-white/90 shadow-sm"
                  : "w-2 h-2 bg-white/30 hover:bg-white/50 hover:scale-105"
              }`}
              aria-label={`الانتقال إلى الشريحة ${index + 1}`}
              style={{
                borderRadius: index === currentSlide ? '8px' : '50%'
              }}
            >
              {index === currentSlide && (
                <div className="absolute inset-0 bg-gradient-to-r from-purple-300/60 to-pink-300/60 rounded-full opacity-70" />
              )}
            </button>
          ))}
        </div>

        {/* Loading indicator for smooth transitions */}
        {isDragging && (
          <div className="absolute inset-0 bg-black/10 pointer-events-none" />
        )}
      </div>
    </div>
  )
}
