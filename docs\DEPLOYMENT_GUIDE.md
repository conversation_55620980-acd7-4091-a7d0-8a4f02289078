# Multi-Tenant Deployment Guide - Bentakon Store

## Overview
This guide provides step-by-step instructions for deploying the Bentakon Store multi-tenant application to production.

## Prerequisites

### Required Services
- [ ] **Vercel Account** (for hosting)
- [ ] **Supabase Project** (for database and authentication)
- [ ] **Domain Name** (optional, for custom domain)
- [ ] **Sentry Account** (for error monitoring)
- [ ] **Google Analytics** (for analytics)

### Required Environment Variables
Copy `.env.example` to `.env.local` and fill in the following:

```bash
# Application
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_APP_NAME="بنتاكون"

# Multi-Tenant Configuration
NEXT_PUBLIC_MULTI_TENANT_MODE=true
NEXT_PUBLIC_DEFAULT_TENANT_SLUG=main
NEXT_PUBLIC_DEFAULT_TENANT_ID=
NEXT_PUBLIC_ENABLE_CUSTOM_DOMAINS=true
NEXT_PUBLIC_ENABLE_SUBDOMAINS=true

# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Security
NEXTAUTH_SECRET=your_32_character_secret
ENCRYPTION_KEY=your_32_character_encryption_key
JWT_SECRET=your_jwt_secret

# Monitoring
SENTRY_DSN=your_sentry_dsn
GOOGLE_ANALYTICS_ID=your_ga_id

# WhatsApp (for customer support)
WHATSAPP_PHONE_NUMBER=+**********
```

## Step 1: Supabase Setup

### 1.1 Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Note down your project URL and anon key

### 1.2 Database Schema Setup
Run the following SQL in Supabase SQL Editor:

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create user profiles table
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  name TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('admin', 'distributor', 'user')),
  wallet_balance DECIMAL(10,2) DEFAULT 0.00,
  avatar TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create products table
CREATE TABLE products (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  slug TEXT UNIQUE NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  cover_image TEXT NOT NULL,
  category TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  rating DECIMAL(2,1) DEFAULT 0.0,
  comment_count INTEGER DEFAULT 0,
  featured BOOLEAN DEFAULT FALSE,
  popular BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create packages table
CREATE TABLE packages (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  original_price DECIMAL(10,2),
  image TEXT NOT NULL,
  description TEXT,
  has_digital_codes BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create orders table
CREATE TABLE orders (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id),
  package_id UUID REFERENCES packages(id),
  amount DECIMAL(10,2) NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed')),
  custom_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create digital codes table
CREATE TABLE digital_codes (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  package_id UUID REFERENCES packages(id) ON DELETE CASCADE,
  key_encrypted TEXT NOT NULL,
  used BOOLEAN DEFAULT FALSE,
  assigned_to_order_id UUID REFERENCES orders(id),
  assigned_at TIMESTAMP WITH TIME ZONE,
  viewed_count INTEGER DEFAULT 0,
  last_viewed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create banner slides table
CREATE TABLE banner_slides (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  subtitle TEXT,
  image TEXT NOT NULL,
  link_type TEXT NOT NULL CHECK (link_type IN ('product', 'collection', 'custom', 'none')),
  link_value TEXT,
  active BOOLEAN DEFAULT TRUE,
  order_index INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create homepage sections table
CREATE TABLE homepage_sections (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title TEXT NOT NULL,
  emoji TEXT,
  product_ids UUID[],
  order_index INTEGER NOT NULL,
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 1.3 Row Level Security (RLS)
Enable RLS and create policies:

```sql
-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE digital_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE banner_slides ENABLE ROW LEVEL SECURITY;
ALTER TABLE homepage_sections ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);

-- Products policies (public read, admin write)
CREATE POLICY "Products are viewable by everyone" ON products FOR SELECT USING (true);
CREATE POLICY "Only admins can modify products" ON products FOR ALL USING (
  EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Orders policies
CREATE POLICY "Users can view own orders" ON orders FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Users can create own orders" ON orders FOR INSERT WITH CHECK (user_id = auth.uid());

-- Add similar policies for other tables...
```

## Step 2: Vercel Deployment

### 2.1 Connect Repository
1. Go to [vercel.com](https://vercel.com)
2. Import your GitHub repository
3. Configure build settings:
   - Framework Preset: Next.js
   - Build Command: `npm run build`
   - Output Directory: `.next`

### 2.2 Environment Variables
Add all environment variables in Vercel dashboard:
1. Go to Project Settings → Environment Variables
2. Add each variable from your `.env.local`
3. Make sure to set `NODE_ENV=production`

### 2.3 Domain Configuration
1. Add your custom domain in Vercel dashboard
2. Configure DNS records as instructed
3. Enable automatic HTTPS

## Step 3: Monitoring Setup

### 3.1 Sentry Configuration
1. Create Sentry project
2. Add `SENTRY_DSN` to environment variables
3. Sentry will automatically capture errors

### 3.2 Google Analytics
1. Create GA4 property
2. Add `GOOGLE_ANALYTICS_ID` to environment variables
3. Analytics will start tracking automatically

## Step 4: Security Checklist

### 4.1 Environment Variables
- [ ] All secrets are properly set
- [ ] No sensitive data in client-side code
- [ ] Environment variables are not logged

### 4.2 Database Security
- [ ] RLS policies are enabled
- [ ] Service role key is secure
- [ ] Database backups are configured

### 4.3 Application Security
- [ ] HTTPS is enforced
- [ ] Security headers are configured
- [ ] Input validation is implemented
- [ ] Digital codes are encrypted

## Step 5: Performance Optimization

### 5.1 Build Optimization
```bash
# Analyze bundle size
npm run build
npx @next/bundle-analyzer
```

### 5.2 Image Optimization
- [ ] Images are optimized and compressed
- [ ] Next.js Image component is used
- [ ] Proper image formats (WebP) are used

### 5.3 Caching Strategy
- [ ] Static assets are cached
- [ ] API responses are cached appropriately
- [ ] Database queries are optimized

## Step 6: Testing in Production

### 6.1 Functionality Testing
- [ ] User registration and login
- [ ] Product browsing and search
- [ ] Order placement and management
- [ ] Admin dashboard functionality
- [ ] Payment processing (if implemented)

### 6.2 Performance Testing
- [ ] Page load times < 3 seconds
- [ ] Core Web Vitals are good
- [ ] Mobile performance is optimized

### 6.3 Security Testing
- [ ] Authentication flows work correctly
- [ ] Authorization is properly enforced
- [ ] No sensitive data is exposed

## Step 7: Post-Deployment

### 7.1 Monitoring
- [ ] Set up error alerts in Sentry
- [ ] Monitor performance metrics
- [ ] Set up uptime monitoring

### 7.2 Backup Strategy
- [ ] Database backups are automated
- [ ] Code is backed up in version control
- [ ] Environment variables are documented

### 7.3 Maintenance
- [ ] Regular dependency updates
- [ ] Security patch monitoring
- [ ] Performance monitoring

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check TypeScript errors
   - Verify all dependencies are installed
   - Check environment variables

2. **Database Connection Issues**
   - Verify Supabase URL and keys
   - Check RLS policies
   - Ensure database schema is correct

3. **Authentication Problems**
   - Check NEXTAUTH_SECRET is set
   - Verify Supabase auth configuration
   - Check redirect URLs

### Support Resources
- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Vercel Documentation](https://vercel.com/docs)

## Rollback Plan

If deployment fails:
1. Revert to previous Vercel deployment
2. Check error logs in Vercel and Sentry
3. Fix issues in development
4. Redeploy with fixes

## Success Criteria

Deployment is successful when:
- [ ] Application loads without errors
- [ ] All core functionality works
- [ ] Performance metrics are acceptable
- [ ] Security measures are in place
- [ ] Monitoring is active
