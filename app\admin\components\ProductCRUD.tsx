"use client"

import { useState } from "react"
import { Plus, Edit, Trash2, X, Upload, Key, AlertCircle } from "lucide-react"
import type { Product, Package, CustomField } from "../../types"
import { useData } from "../../contexts/DataContext"
import { useToast } from "../../components/Toast"
import { LoadingButton, ProductGridSkeleton } from "../../components/LoadingStates"

export default function ProductCRUD() {
  // Use centralized data context
  const { products, updateProduct, deleteProduct, addProduct, isLoading, error } = useData()
  const toast = useToast()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState<Partial<Product>>({
    title: "",
    description: "",
    category: "",
    tags: [],
    coverImage: "",
    packages: [],
    customFields: [],
    dropdowns: [],
    featured: false,
    popular: false,
  })

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      category: "",
      tags: [],
      coverImage: "",
      packages: [],
      customFields: [],
      dropdowns: [],
      featured: false,
      popular: false,
    })
    setEditingProduct(null)
  }

  const openModal = (product?: Product) => {
    if (product) {
      setEditingProduct(product)
      setFormData(product)
    } else {
      resetForm()
    }
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    resetForm()
  }

  const handleSave = async () => {
    setIsSubmitting(true)

    try {
      let result

      if (editingProduct) {
        // Update existing product
        const updatedProduct = {
          ...formData,
          id: editingProduct.id,
          slug: editingProduct.slug
        } as Product

        result = await updateProduct(updatedProduct)

        if (result.success) {
          toast.success('تم تحديث المنتج بنجاح')
        } else {
          toast.error('فشل في تحديث المنتج', result.error)
        }
      } else {
        // Create new product
        const newProduct: Product = {
          ...formData,
          id: Date.now().toString(),
          slug: formData.title?.toLowerCase().replace(/\s+/g, "-") || "",
          rating: 0,
          commentCount: 0,
        } as Product

        result = await addProduct(newProduct)

        if (result.success) {
          toast.success('تم إضافة المنتج بنجاح')
        } else {
          toast.error('فشل في إضافة المنتج', result.error)
        }
      }

      if (result.success) {
        closeModal()
      }
    } catch (error) {
      toast.error('حدث خطأ غير متوقع')
      console.error('Product save error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async (productId: string) => {
    if (!confirm("هل أنت متأكد من حذف هذا المنتج؟")) return

    try {
      const result = await deleteProduct(productId)

      if (result.success) {
        toast.success('تم حذف المنتج بنجاح')
      } else {
        toast.error('فشل في حذف المنتج', result.error)
      }
    } catch (error) {
      toast.error('حدث خطأ غير متوقع')
      console.error('Product delete error:', error)
    }
  }

  const addPackage = () => {
    const newPackage: Package = {
      id: Date.now().toString(),
      name: "",
      price: 0,
      image: "",
      hasDigitalCodes: false,
      digitalCodes: [],
    }
    setFormData((prev) => ({
      ...prev,
      packages: [...(prev.packages || []), newPackage],
    }))
  }

  const updatePackage = (index: number, field: keyof Package, value: any) => {
    setFormData((prev) => ({
      ...prev,
      packages: prev.packages?.map((pkg, i) => (i === index ? { ...pkg, [field]: value } : pkg)) || [],
    }))
  }

  const removePackage = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      packages: prev.packages?.filter((_, i) => i !== index) || [],
    }))
  }

  const updatePackageDigitalCodes = (packageIndex: number, codesText: string) => {
    const codes = codesText
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0)
      .map((key, index) => ({
        id: `${Date.now()}-${index}`,
        key,
        used: false,
        assignedToOrderId: null,
      }))

    setFormData((prev) => ({
      ...prev,
      packages:
        prev.packages?.map((pkg, i) =>
          i === packageIndex
            ? {
                ...pkg,
                digitalCodes: codes,
                hasDigitalCodes: codes.length > 0,
                availableCodesCount: codes.length,
              }
            : pkg,
        ) || [],
    }))
  }

  const getPackageDigitalCodesText = (pkg: Package) => {
    return pkg.digitalCodes?.map((code) => code.key).join("\n") || ""
  }

  const addCustomField = () => {
    const newField: CustomField = {
      id: Date.now().toString(),
      label: "",
      type: "text",
      required: false,
      placeholder: "",
    }
    setFormData((prev) => ({
      ...prev,
      customFields: [...(prev.customFields || []), newField],
    }))
  }

  const updateCustomField = (index: number, field: keyof CustomField, value: any) => {
    setFormData((prev) => ({
      ...prev,
      customFields:
        prev.customFields?.map((customField, i) => (i === index ? { ...customField, [field]: value } : customField)) ||
        [],
    }))
  }

  const removeCustomField = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      customFields: prev.customFields?.filter((_, i) => i !== index) || [],
    }))
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h2 className="text-xl md:text-2xl font-bold">إدارة المنتجات</h2>
        <button onClick={() => openModal()} className="btn-primary flex items-center justify-center space-x-2 space-x-reverse w-full sm:w-auto">
          <Plus className="w-5 h-5" />
          <span>إضافة منتج</span>
        </button>
      </div>

      {/* Products Display */}
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
        <div className="p-4 md:p-6">
          {/* Desktop Table */}
          <div className="hidden lg:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-right text-gray-400 text-sm">
                  <th className="pb-3">المنتج</th>
                  <th className="pb-3">الفئة</th>
                  <th className="pb-3">الحزم</th>
                  <th className="pb-3">الأكواد الرقمية</th>
                  <th className="pb-3">التقييم</th>
                  <th className="pb-3">الحالة</th>
                  <th className="pb-3">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {products.map((product) => {
                  const totalCodes = product.packages.reduce((sum, pkg) => sum + (pkg.digitalCodes?.length || 0), 0)
                  const availableCodes = product.packages.reduce((sum, pkg) => sum + (pkg.availableCodesCount || 0), 0)
                  const hasDigitalPackages = product.packages.some((pkg) => pkg.hasDigitalCodes)

                  return (
                    <tr key={product.id} className="border-t border-gray-700/50">
                      <td className="py-4">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <img
                            src={product.coverImage || "/logo.jpg"}
                            alt={product.title}
                            className="w-12 h-12 rounded-lg object-cover"
                          />
                          <div>
                            <p className="font-semibold">{product.title}</p>
                            <p className="text-sm text-gray-400">{product.slug}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4">{product.category}</td>
                      <td className="py-4">{product.packages.length} حزمة</td>
                      <td className="py-4">
                        {hasDigitalPackages ? (
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <Key className="w-4 h-4 text-blue-400" />
                            <span className="text-sm">
                              {availableCodes}/{totalCodes}
                            </span>
                            {availableCodes === 0 && totalCodes > 0 && (
                              <AlertCircle className="w-4 h-4 text-red-400" aria-label="نفدت الأكواد" />
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-500 text-sm">لا يوجد</span>
                        )}
                      </td>
                      <td className="py-4">
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <span>{product.rating}</span>
                          <span className="text-gray-400">({product.commentCount})</span>
                        </div>
                      </td>
                      <td className="py-4">
                        <div className="flex space-x-2 space-x-reverse">
                          {product.featured && (
                            <span className="px-2 py-1 bg-purple-400/10 text-purple-400 rounded-xl text-xs">مميز</span>
                          )}
                          {product.popular && (
                            <span className="px-2 py-1 bg-blue-400/10 text-blue-400 rounded-xl text-xs">شائع</span>
                          )}
                        </div>
                      </td>
                      <td className="py-4">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => openModal(product)}
                            className="p-2 text-gray-400 hover:text-purple-400 transition-colors rounded-xl hover:bg-purple-400/10"
                            title="تعديل"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(product.id)}
                            className="p-2 text-gray-400 hover:text-red-400 transition-colors rounded-xl hover:bg-red-400/10"
                            disabled={isLoading}
                            title="حذف"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="lg:hidden space-y-4">
            {products.map((product) => {
              const totalCodes = product.packages.reduce((sum, pkg) => sum + (pkg.digitalCodes?.length || 0), 0)
              const availableCodes = product.packages.reduce((sum, pkg) => sum + (pkg.availableCodesCount || 0), 0)
              const hasDigitalPackages = product.packages.some((pkg) => pkg.hasDigitalCodes)

              return (
                <div key={product.id} className="bg-gray-700/30 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50">
                  {/* Product Header */}
                  <div className="flex items-start space-x-3 space-x-reverse mb-4">
                    <img
                      src={product.coverImage || "/logo.jpg"}
                      alt={product.title}
                      className="w-16 h-16 rounded-xl object-cover flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-lg truncate">{product.title}</h3>
                      <p className="text-sm text-gray-400 truncate">{product.slug}</p>
                      <div className="flex items-center space-x-2 space-x-reverse mt-2">
                        {product.featured && (
                          <span className="px-2 py-1 bg-purple-400/10 text-purple-400 rounded-lg text-xs">مميز</span>
                        )}
                        {product.popular && (
                          <span className="px-2 py-1 bg-blue-400/10 text-blue-400 rounded-lg text-xs">شائع</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Product Details Grid */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-xs text-gray-400 mb-1">الفئة</p>
                      <p className="text-sm font-medium">{product.category}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-400 mb-1">الحزم</p>
                      <p className="text-sm font-medium">{product.packages.length} حزمة</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-400 mb-1">التقييم</p>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span className="text-sm font-medium">{product.rating}</span>
                        <span className="text-xs text-gray-400">({product.commentCount})</span>
                      </div>
                    </div>
                    <div>
                      <p className="text-xs text-gray-400 mb-1">الأكواد الرقمية</p>
                      {hasDigitalPackages ? (
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Key className="w-3 h-3 text-blue-400" />
                          <span className="text-sm font-medium">
                            {availableCodes}/{totalCodes}
                          </span>
                          {availableCodes === 0 && totalCodes > 0 && (
                            <AlertCircle className="w-3 h-3 text-red-400" aria-label="نفدت الأكواد" />
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-500 text-sm">لا يوجد</span>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-3 space-x-reverse pt-3 border-t border-gray-600/50">
                    <button
                      onClick={() => openModal(product)}
                      className="flex-1 flex items-center justify-center space-x-2 space-x-reverse px-4 py-2 bg-purple-600/20 text-purple-400 hover:bg-purple-600/30 transition-colors rounded-xl"
                    >
                      <Edit className="w-4 h-4" />
                      <span className="text-sm font-medium">تعديل</span>
                    </button>
                    <button
                      onClick={() => handleDelete(product.id)}
                      className="flex-1 flex items-center justify-center space-x-2 space-x-reverse px-4 py-2 bg-red-600/20 text-red-400 hover:bg-red-600/30 transition-colors rounded-xl"
                      disabled={isLoading}
                    >
                      <Trash2 className="w-4 h-4" />
                      <span className="text-sm font-medium">حذف</span>
                    </button>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-2 md:p-4">
          <div className="bg-gray-800/90 backdrop-blur-md rounded-xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/50 shadow-2xl">
            <div className="p-4 md:p-6 border-b border-gray-700/50">
              <div className="flex items-center justify-between">
                <h3 className="text-lg md:text-2xl font-bold">{editingProduct ? "تعديل المنتج" : "إضافة منتج جديد"}</h3>
                <button onClick={closeModal} className="text-gray-400 hover:text-white p-2 rounded-xl hover:bg-gray-700/50 transition-colors">
                  <X className="w-5 h-5 md:w-6 md:h-6" />
                </button>
              </div>
            </div>

            <div className="p-4 md:p-6 space-y-4 md:space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2">اسم المنتج</label>
                  <input
                    type="text"
                    value={formData.title || ""}
                    onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    placeholder="أدخل اسم المنتج"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">الفئة</label>
                  <input
                    type="text"
                    value={formData.category || ""}
                    onChange={(e) => setFormData((prev) => ({ ...prev, category: e.target.value }))}
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    placeholder="مثل: MOBA, RPG, باتل رويال"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">الوصف</label>
                <textarea
                  value={formData.description || ""}
                  onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                  placeholder="وصف المنتج"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">العلامات (مفصولة بفاصلة)</label>
                <input
                  type="text"
                  value={formData.tags?.join(", ") || ""}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      tags: e.target.value
                        .split(",")
                        .map((tag) => tag.trim())
                        .filter(Boolean),
                    }))
                  }
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                  placeholder="شائع, مميز, جديد"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">صورة الغلاف</label>
                <div className="flex items-center space-x-3 space-x-reverse">
                  <input
                    type="text"
                    value={formData.coverImage || ""}
                    onChange={(e) => setFormData((prev) => ({ ...prev, coverImage: e.target.value }))}
                    className="flex-1 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    placeholder="رابط الصورة"
                  />
                  <button className="btn-secondary flex items-center space-x-2 space-x-reverse">
                    <Upload className="w-4 h-4" />
                    <span>رفع</span>
                  </button>
                </div>
              </div>

              {/* Status Toggles */}
              <div className="flex space-x-6 space-x-reverse">
                <label className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    checked={formData.featured || false}
                    onChange={(e) => setFormData((prev) => ({ ...prev, featured: e.target.checked }))}
                    className="rounded"
                  />
                  <span>منتج مميز</span>
                </label>
                <label className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    checked={formData.popular || false}
                    onChange={(e) => setFormData((prev) => ({ ...prev, popular: e.target.checked }))}
                    className="rounded"
                  />
                  <span>منتج شائع</span>
                </label>
              </div>

              {/* Packages */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-semibold">الحزم</h4>
                  <button onClick={addPackage} className="btn-secondary flex items-center space-x-2 space-x-reverse">
                    <Plus className="w-4 h-4" />
                    <span>إضافة حزمة</span>
                  </button>
                </div>

                <div className="space-y-6">
                  {formData.packages?.map((pkg, index) => (
                    <div
                      key={index}
                      className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-6 border border-gray-600/50"
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 mb-4">
                        <input
                          type="text"
                          value={pkg.name}
                          onChange={(e) => updatePackage(index, "name", e.target.value)}
                          placeholder="اسم الحزمة"
                          className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                        />
                        <input
                          type="number"
                          step="0.01"
                          value={pkg.price}
                          onChange={(e) => updatePackage(index, "price", Number(e.target.value))}
                          placeholder="السعر"
                          className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                        />
                        <input
                          type="number"
                          step="0.01"
                          value={pkg.originalPrice || ""}
                          onChange={(e) => updatePackage(index, "originalPrice", Number(e.target.value) || undefined)}
                          placeholder="السعر الأصلي"
                          className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                        />
                        <input
                          type="number"
                          value={pkg.discount || ""}
                          onChange={(e) => updatePackage(index, "discount", Number(e.target.value) || undefined)}
                          placeholder="نسبة الخصم (%)"
                          className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                        />
                      </div>

                      <div className="grid grid-cols-1 gap-4 mb-4">
                        <input
                          type="text"
                          value={pkg.image}
                          onChange={(e) => updatePackage(index, "image", e.target.value)}
                          placeholder="رابط صورة الحزمة"
                          className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                        />
                        <textarea
                          value={pkg.description || ""}
                          onChange={(e) => updatePackage(index, "description", e.target.value)}
                          placeholder="وصف الحزمة (اختياري)"
                          rows={2}
                          className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 resize-none"
                        />
                      </div>

                      {/* Digital Codes Section */}
                      <div className="border-t border-gray-600/50 pt-4">
                        <div className="flex items-center space-x-2 space-x-reverse mb-3">
                          <Key className="w-5 h-5 text-blue-400" />
                          <h5 className="font-semibold text-blue-400">الأكواد الرقمية</h5>
                          <span className="text-sm text-gray-400">(اختياري)</span>
                        </div>

                        <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3">
                          <p className="text-sm text-blue-300 mb-2">💡 إرشادات الأكواد الرقمية:</p>
                          <ul className="text-xs text-blue-200 space-y-1">
                            <li>• أدخل كود واحد في كل سطر</li>
                            <li>• سيتم تخصيص كود واحد فقط لكل طلب</li>
                            <li>• الأكواد المستخدمة لن تظهر للمشترين الآخرين</li>
                            <li>• إذا نفدت الأكواد، ستصبح الحزمة غير متاحة</li>
                          </ul>
                        </div>

                        <textarea
                          value={getPackageDigitalCodesText(pkg)}
                          onChange={(e) => updatePackageDigitalCodes(index, e.target.value)}
                          placeholder="أدخل الأكواد الرقمية (كود واحد في كل سطر)&#10;مثال:&#10;AB12-XY34-ZZ78&#10;CD56-PL90-QW12&#10;9GHT-LMK3-992Z"
                          rows={4}
                          className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 font-mono text-sm resize-none"
                        />

                        {pkg.digitalCodes && pkg.digitalCodes.length > 0 && (
                          <div className="mt-2 text-sm text-green-400">
                            ✅ تم إضافة {pkg.digitalCodes.length} كود رقمي
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-end mt-4">
                        <button
                          onClick={() => removePackage(index)}
                          className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-400/10 transition-all duration-300"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Custom Fields */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-semibold">الحقول المخصصة</h4>
                  <button
                    onClick={addCustomField}
                    className="btn-secondary flex items-center space-x-2 space-x-reverse"
                  >
                    <Plus className="w-4 h-4" />
                    <span>إضافة حقل</span>
                  </button>
                </div>

                <div className="space-y-4">
                  {formData.customFields?.map((field, index) => (
                    <div
                      key={index}
                      className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50"
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 mb-4">
                        <input
                          type="text"
                          value={field.label}
                          onChange={(e) => updateCustomField(index, "label", e.target.value)}
                          placeholder="تسمية الحقل"
                          className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                        />
                        <select
                          value={field.type}
                          onChange={(e) => updateCustomField(index, "type", e.target.value)}
                          className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                        >
                          <option value="text">نص</option>
                          <option value="email">بريد إلكتروني</option>
                          <option value="number">رقم</option>
                        </select>
                        <input
                          type="text"
                          value={field.placeholder}
                          onChange={(e) => updateCustomField(index, "placeholder", e.target.value)}
                          placeholder="النص التوضيحي"
                          className="bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <label className="flex items-center space-x-2 space-x-reverse">
                          <input
                            type="checkbox"
                            checked={field.required}
                            onChange={(e) => updateCustomField(index, "required", e.target.checked)}
                            className="rounded"
                          />
                          <span>مطلوب</span>
                        </label>
                        <button
                          onClick={() => removeCustomField(index)}
                          className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-400/10 transition-all duration-300"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4 space-x-reverse pt-6 border-t border-gray-700/50">
                <LoadingButton
                  onClick={handleSave}
                  loading={isSubmitting}
                  className="flex-1 btn-primary"
                >
                  {editingProduct ? "تحديث المنتج" : "إضافة المنتج"}
                </LoadingButton>
                <button onClick={closeModal} disabled={isSubmitting} className="flex-1 btn-secondary">
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
