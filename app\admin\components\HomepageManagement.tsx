"use client"

import { useState } from "react"
import { Plus, Edit, Trash2, X, Upload, Save, ArrowUp, ArrowDown, Eye, EyeOff, ImageIcon } from "lucide-react"
import type { BannerSlide, HomepageSection } from "../../types"
import { useData } from "../../contexts/DataContext"
import { sanitizeString, sanitizeUrl } from "../../lib/validations"

export default function HomepageManagement() {
  // Use centralized data context
  const {
    banners,
    setBanners,
    homepageSections: sections,
    setHomepageSections,
    products,
    updateBanner,
    deleteBanner,
    addBanner,
    updateHomepageSection,
    deleteHomepageSection,
    addHomepageSection
  } = useData()

  const [activeTab, setActiveTab] = useState<"banners" | "sections">("banners")
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<"banner" | "section">("banner")
  const [editingItem, setEditingItem] = useState<BannerSlide | HomepageSection | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Banner form state
  const [bannerForm, setBannerForm] = useState<Partial<BannerSlide>>({
    title: "",
    subtitle: "",
    image: "",
    linkType: "none",
    linkValue: "",
    active: true,
    order: 1,
  })

  // Section form state
  const [sectionForm, setSectionForm] = useState<Partial<HomepageSection>>({
    title: "",
    emoji: "",
    productIds: [],
    order: 1,
    active: true,
  })

  const resetForms = () => {
    setBannerForm({
      title: "",
      subtitle: "",
      image: "",
      linkType: "none",
      linkValue: "",
      active: true,
      order: 1,
    })
    setSectionForm({
      title: "",
      emoji: "",
      productIds: [],
      order: 1,
      active: true,
    })
    setEditingItem(null)
  }

  const openModal = (type: "banner" | "section", item?: BannerSlide | HomepageSection) => {
    setModalType(type)
    if (item) {
      setEditingItem(item)
      if (type === "banner") {
        setBannerForm(item as BannerSlide)
      } else {
        setSectionForm(item as HomepageSection)
      }
    } else {
      resetForms()
      if (type === "banner") {
        setBannerForm((prev) => ({ ...prev, order: banners.length + 1 }))
      } else {
        setSectionForm((prev) => ({ ...prev, order: sections.length + 1 }))
      }
    }
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    resetForms()
  }

  const handleSave = async () => {
    setIsLoading(true)

    // TODO: Implement with Supabase
    await new Promise((resolve) => setTimeout(resolve, 1000))

    if (modalType === "banner") {
      // Sanitize banner data
      const sanitizedBannerForm = {
        ...bannerForm,
        title: sanitizeString(bannerForm.title || ''),
        subtitle: sanitizeString(bannerForm.subtitle || ''),
        image: sanitizeUrl(bannerForm.image || ''),
        linkValue: bannerForm.linkValue ? sanitizeUrl(bannerForm.linkValue) : ''
      }

      if (editingItem) {
        // Update existing banner
        setBanners((prev) =>
          prev.map((banner) =>
            banner.id === editingItem.id ? ({ ...sanitizedBannerForm, id: editingItem.id } as BannerSlide) : banner,
          ),
        )
      } else {
        // Create new banner
        const newBanner: BannerSlide = {
          ...sanitizedBannerForm,
          id: crypto.randomUUID(),
        } as BannerSlide
        setBanners((prev) => [...prev, newBanner])
      }
    } else {
      // Sanitize section data
      const sanitizedSectionForm = {
        ...sectionForm,
        title: sanitizeString(sectionForm.title || ''),
        emoji: sanitizeString(sectionForm.emoji || '')
      }

      if (editingItem) {
        // Update existing section
        setHomepageSections((prev) =>
          prev.map((section) =>
            section.id === editingItem.id ? ({ ...sanitizedSectionForm, id: editingItem.id } as HomepageSection) : section,
          ),
        )
      } else {
        // Create new section
        const newSection: HomepageSection = {
          ...sanitizedSectionForm,
          id: crypto.randomUUID(),
        } as HomepageSection
        setHomepageSections((prev) => [...prev, newSection])
      }
    }

    setIsLoading(false)
    closeModal()
  }

  const handleDelete = async (type: "banner" | "section", id: string) => {
    if (!confirm("هل أنت متأكد من حذف هذا العنصر؟")) return

    setIsLoading(true)
    // TODO: Implement with Supabase
    await new Promise((resolve) => setTimeout(resolve, 500))

    if (type === "banner") {
      setBanners((prev) => prev.filter((banner) => banner.id !== id))
    } else {
      setHomepageSections((prev) => prev.filter((section) => section.id !== id))
    }
    setIsLoading(false)
  }

  const moveItem = (type: "banner" | "section", id: string, direction: "up" | "down") => {
    if (type === "banner") {
      setBanners((prev) => {
        const sorted = [...prev].sort((a, b) => a.order - b.order)
        const index = sorted.findIndex((item) => item.id === id)
        if ((direction === "up" && index === 0) || (direction === "down" && index === sorted.length - 1)) {
          return prev
        }

        const newIndex = direction === "up" ? index - 1 : index + 1
        const temp = sorted[index].order
        sorted[index].order = sorted[newIndex].order
        sorted[newIndex].order = temp

        return sorted
      })
    } else {
      setHomepageSections((prev) => {
        const sorted = [...prev].sort((a, b) => a.order - b.order)
        const index = sorted.findIndex((item) => item.id === id)
        if ((direction === "up" && index === 0) || (direction === "down" && index === sorted.length - 1)) {
          return prev
        }

        const newIndex = direction === "up" ? index - 1 : index + 1
        const temp = sorted[index].order
        sorted[index].order = sorted[newIndex].order
        sorted[newIndex].order = temp

        return sorted
      })
    }
  }

  const toggleActive = (type: "banner" | "section", id: string) => {
    if (type === "banner") {
      setBanners((prev) => prev.map((banner) => (banner.id === id ? { ...banner, active: !banner.active } : banner)))
    } else {
      setHomepageSections((prev) =>
        prev.map((section) => (section.id === id ? { ...section, active: !section.active } : section)),
      )
    }
  }

  const handleProductSelection = (productId: string, selected: boolean) => {
    setSectionForm((prev) => ({
      ...prev,
      productIds: selected
        ? [...(prev.productIds || []), productId]
        : (prev.productIds || []).filter((id) => id !== productId),
    }))
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h2 className="text-xl md:text-2xl font-bold">إدارة الصفحة الرئيسية</h2>
      </div>

      {/* Tabs */}
      <div className="flex space-x-4 space-x-reverse border-b border-gray-700/50 overflow-x-auto">
        <button
          onClick={() => setActiveTab("banners")}
          className={`pb-3 px-3 md:px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
            activeTab === "banners"
              ? "border-purple-500 text-purple-400"
              : "border-transparent text-gray-400 hover:text-gray-300"
          }`}
        >
          إدارة البانرات
        </button>
        <button
          onClick={() => setActiveTab("sections")}
          className={`pb-3 px-3 md:px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
            activeTab === "sections"
              ? "border-purple-500 text-purple-400"
              : "border-transparent text-gray-400 hover:text-gray-300"
          }`}
        >
          أقسام المنتجات
        </button>
      </div>

      {/* Banner Management */}
      {activeTab === "banners" && (
        <div className="space-y-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <h3 className="text-lg md:text-xl font-semibold">البانرات</h3>
            <button
              onClick={() => openModal("banner")}
              className="btn-primary flex items-center justify-center space-x-2 space-x-reverse w-full sm:w-auto"
            >
              <Plus className="w-5 h-5" />
              <span>إضافة بانر</span>
            </button>
          </div>

          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
            <div className="p-4 md:p-6">
              <div className="space-y-4">
                {banners
                  .sort((a, b) => a.order - b.order)
                  .map((banner) => (
                    <div
                      key={banner.id}
                      className={`bg-gray-700/30 backdrop-blur-sm rounded-xl p-4 border transition-all duration-300 ${
                        banner.active ? "border-gray-600/50" : "border-gray-600/30 opacity-60"
                      }`}
                    >
                      {/* Desktop Layout */}
                      <div className="hidden md:flex items-center justify-between">
                        <div className="flex items-center space-x-4 space-x-reverse">
                          <div className="relative w-20 h-12 rounded-xl overflow-hidden bg-gray-600 flex-shrink-0">
                            {banner.image ? (
                              <img
                                src={banner.image || "/logo.jpg"}
                                alt={banner.title}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <ImageIcon className="w-6 h-6 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <div>
                            <h4 className="font-semibold">{banner.title}</h4>
                            {banner.subtitle && <p className="text-sm text-gray-400">{banner.subtitle}</p>}
                            <div className="flex items-center space-x-2 space-x-reverse text-xs text-gray-500 mt-1">
                              <span>ترتيب: {banner.order}</span>
                              <span>•</span>
                              <span>
                                {banner.linkType === "product"
                                  ? "منتج"
                                  : banner.linkType === "collection"
                                    ? "مجموعة"
                                    : banner.linkType === "custom"
                                      ? "رابط مخصص"
                                      : "بدون رابط"}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => moveItem("banner", banner.id, "up")}
                            className="p-2 text-gray-400 hover:text-purple-400 transition-colors rounded-xl hover:bg-purple-400/10"
                            title="تحريك لأعلى"
                          >
                            <ArrowUp className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => moveItem("banner", banner.id, "down")}
                            className="p-2 text-gray-400 hover:text-purple-400 transition-colors rounded-xl hover:bg-purple-400/10"
                            title="تحريك لأسفل"
                          >
                            <ArrowDown className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => toggleActive("banner", banner.id)}
                            className={`p-2 transition-colors rounded-xl ${
                              banner.active
                                ? "text-green-400 hover:text-green-300 hover:bg-green-400/10"
                                : "text-gray-400 hover:text-gray-300 hover:bg-gray-400/10"
                            }`}
                            title={banner.active ? "إخفاء" : "إظهار"}
                          >
                            {banner.active ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                          </button>
                          <button
                            onClick={() => openModal("banner", banner)}
                            className="p-2 text-gray-400 hover:text-purple-400 transition-colors rounded-xl hover:bg-purple-400/10"
                            title="تعديل"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete("banner", banner.id)}
                            className="p-2 text-gray-400 hover:text-red-400 transition-colors rounded-xl hover:bg-red-400/10"
                            title="حذف"
                            disabled={isLoading}
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>

                      {/* Mobile Layout */}
                      <div className="md:hidden">
                        <div className="flex items-start space-x-3 space-x-reverse mb-4">
                          <div className="relative w-16 h-10 rounded-xl overflow-hidden bg-gray-600 flex-shrink-0">
                            {banner.image ? (
                              <img
                                src={banner.image || "/logo.jpg"}
                                alt={banner.title}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <ImageIcon className="w-4 h-4 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold text-lg truncate">{banner.title}</h4>
                            {banner.subtitle && <p className="text-sm text-gray-400 truncate">{banner.subtitle}</p>}
                            <div className="flex items-center space-x-2 space-x-reverse text-xs text-gray-500 mt-1">
                              <span>ترتيب: {banner.order}</span>
                              <span>•</span>
                              <span>
                                {banner.linkType === "product"
                                  ? "منتج"
                                  : banner.linkType === "collection"
                                    ? "مجموعة"
                                    : banner.linkType === "custom"
                                      ? "رابط مخصص"
                                      : "بدون رابط"}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Mobile Action Buttons */}
                        <div className="grid grid-cols-3 gap-2">
                          <button
                            onClick={() => moveItem("banner", banner.id, "up")}
                            className="flex items-center justify-center space-x-1 space-x-reverse px-3 py-2 bg-purple-600/20 text-purple-400 hover:bg-purple-600/30 transition-colors rounded-xl"
                          >
                            <ArrowUp className="w-4 h-4" />
                            <span className="text-xs">أعلى</span>
                          </button>
                          <button
                            onClick={() => moveItem("banner", banner.id, "down")}
                            className="flex items-center justify-center space-x-1 space-x-reverse px-3 py-2 bg-purple-600/20 text-purple-400 hover:bg-purple-600/30 transition-colors rounded-xl"
                          >
                            <ArrowDown className="w-4 h-4" />
                            <span className="text-xs">أسفل</span>
                          </button>
                          <button
                            onClick={() => toggleActive("banner", banner.id)}
                            className={`flex items-center justify-center space-x-1 space-x-reverse px-3 py-2 transition-colors rounded-xl ${
                              banner.active
                                ? "bg-green-600/20 text-green-400 hover:bg-green-600/30"
                                : "bg-gray-600/20 text-gray-400 hover:bg-gray-600/30"
                            }`}
                          >
                            {banner.active ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                            <span className="text-xs">{banner.active ? "مرئي" : "مخفي"}</span>
                          </button>
                          <button
                            onClick={() => openModal("banner", banner)}
                            className="flex items-center justify-center space-x-1 space-x-reverse px-3 py-2 bg-blue-600/20 text-blue-400 hover:bg-blue-600/30 transition-colors rounded-xl"
                          >
                            <Edit className="w-4 h-4" />
                            <span className="text-xs">تعديل</span>
                          </button>
                          <button
                            onClick={() => handleDelete("banner", banner.id)}
                            className="flex items-center justify-center space-x-1 space-x-reverse px-3 py-2 bg-red-600/20 text-red-400 hover:bg-red-600/30 transition-colors rounded-xl"
                            disabled={isLoading}
                          >
                            <Trash2 className="w-4 h-4" />
                            <span className="text-xs">حذف</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Section Management */}
      {activeTab === "sections" && (
        <div className="space-y-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <h3 className="text-lg md:text-xl font-semibold">أقسام المنتجات</h3>
            <button
              onClick={() => openModal("section")}
              className="btn-primary flex items-center justify-center space-x-2 space-x-reverse w-full sm:w-auto"
            >
              <Plus className="w-5 h-5" />
              <span>إضافة قسم</span>
            </button>
          </div>

          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
            <div className="p-6">
              <div className="space-y-4">
                {sections
                  .sort((a, b) => a.order - b.order)
                  .map((section) => (
                    <div
                      key={section.id}
                      className={`bg-gray-700/30 backdrop-blur-sm rounded-lg p-4 border transition-all duration-300 ${
                        section.active ? "border-gray-600/50" : "border-gray-600/30 opacity-60"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-center space-x-2 space-x-reverse mb-2">
                            {section.emoji && <span className="text-xl">{section.emoji}</span>}
                            <h4 className="font-semibold text-lg">{section.title}</h4>
                          </div>
                          <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-400">
                            <span>ترتيب: {section.order}</span>
                            <span>•</span>
                            <span>{section.productIds.length} منتج</span>
                          </div>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {section.productIds.slice(0, 3).map((productId) => {
                              const product = products.find((p) => p.id === productId)
                              return (
                                <span
                                  key={productId}
                                  className="px-2 py-1 bg-blue-400/10 text-blue-400 rounded text-xs"
                                >
                                  {product?.title.substring(0, 20)}...
                                </span>
                              )
                            })}
                            {section.productIds.length > 3 && (
                              <span className="px-2 py-1 bg-gray-400/10 text-gray-400 rounded text-xs">
                                +{section.productIds.length - 3} أخرى
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => moveItem("section", section.id, "up")}
                            className="p-2 text-gray-400 hover:text-purple-400 transition-colors rounded-lg hover:bg-purple-400/10"
                            title="تحريك لأعلى"
                          >
                            <ArrowUp className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => moveItem("section", section.id, "down")}
                            className="p-2 text-gray-400 hover:text-purple-400 transition-colors rounded-lg hover:bg-purple-400/10"
                            title="تحريك لأسفل"
                          >
                            <ArrowDown className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => toggleActive("section", section.id)}
                            className={`p-2 transition-colors rounded-lg ${
                              section.active
                                ? "text-green-400 hover:text-green-300 hover:bg-green-400/10"
                                : "text-gray-400 hover:text-gray-300 hover:bg-gray-400/10"
                            }`}
                            title={section.active ? "إخفاء" : "إظهار"}
                          >
                            {section.active ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                          </button>
                          <button
                            onClick={() => openModal("section", section)}
                            className="p-2 text-gray-400 hover:text-purple-400 transition-colors rounded-lg hover:bg-purple-400/10"
                            title="تعديل"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete("section", section.id)}
                            className="p-2 text-gray-400 hover:text-red-400 transition-colors rounded-lg hover:bg-red-400/10"
                            title="حذف"
                            disabled={isLoading}
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800/90 backdrop-blur-md rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-gray-700/50 shadow-2xl">
            <div className="p-6 border-b border-gray-700/50">
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-bold">
                  {modalType === "banner"
                    ? editingItem
                      ? "تعديل البانر"
                      : "إضافة بانر جديد"
                    : editingItem
                      ? "تعديل القسم"
                      : "إضافة قسم جديد"}
                </h3>
                <button onClick={closeModal} className="text-gray-400 hover:text-white">
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {modalType === "banner" ? (
                /* Banner Form */
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2">عنوان البانر</label>
                      <input
                        type="text"
                        value={bannerForm.title || ""}
                        onChange={(e) => setBannerForm((prev) => ({ ...prev, title: e.target.value }))}
                        className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                        placeholder="أدخل عنوان البانر"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">العنوان الفرعي (اختياري)</label>
                      <input
                        type="text"
                        value={bannerForm.subtitle || ""}
                        onChange={(e) => setBannerForm((prev) => ({ ...prev, subtitle: e.target.value }))}
                        className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                        placeholder="أدخل العنوان الفرعي"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">صورة البانر</label>
                    <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3">
                      <p className="text-sm text-blue-300 mb-1">📐 متطلبات الصورة:</p>
                      <ul className="text-xs text-blue-200 space-y-1">
                        <li>• نسبة العرض إلى الارتفاع: 3:1 (مثل 1200×400 بكسل)</li>
                        <li>• تنسيق مدعوم: JPG, PNG, WebP</li>
                        <li>• حجم مقترح: 1200×400 بكسل للحصول على أفضل جودة</li>
                      </ul>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <input
                        type="text"
                        value={bannerForm.image || ""}
                        onChange={(e) => setBannerForm((prev) => ({ ...prev, image: e.target.value }))}
                        className="flex-1 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                        placeholder="رابط الصورة"
                      />
                      <button className="btn-secondary flex items-center space-x-2 space-x-reverse">
                        <Upload className="w-4 h-4" />
                        <span>رفع</span>
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2">نوع الرابط</label>
                      <select
                        value={bannerForm.linkType || "none"}
                        onChange={(e) =>
                          setBannerForm((prev) => ({
                            ...prev,
                            linkType: e.target.value as "product" | "collection" | "custom" | "none",
                          }))
                        }
                        className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                      >
                        <option value="none">بدون رابط</option>
                        <option value="product">منتج</option>
                        <option value="collection">مجموعة</option>
                        <option value="custom">رابط مخصص</option>
                      </select>
                    </div>

                    {bannerForm.linkType !== "none" && (
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          {bannerForm.linkType === "product"
                            ? "اختر المنتج"
                            : bannerForm.linkType === "collection"
                              ? "اسم المجموعة"
                              : "الرابط المخصص"}
                        </label>
                        {bannerForm.linkType === "product" ? (
                          <select
                            value={bannerForm.linkValue || ""}
                            onChange={(e) => setBannerForm((prev) => ({ ...prev, linkValue: e.target.value }))}
                            className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                          >
                            <option value="">اختر منتج</option>
                            {products.map((product) => (
                              <option key={product.id} value={product.slug}>
                                {product.title}
                              </option>
                            ))}
                          </select>
                        ) : (
                          <input
                            type="text"
                            value={bannerForm.linkValue || ""}
                            onChange={(e) => setBannerForm((prev) => ({ ...prev, linkValue: e.target.value }))}
                            className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                            placeholder={bannerForm.linkType === "collection" ? "اسم المجموعة" : "https://example.com"}
                          />
                        )}
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2">ترتيب العرض</label>
                      <input
                        type="number"
                        min="1"
                        value={bannerForm.order || 1}
                        onChange={(e) => setBannerForm((prev) => ({ ...prev, order: Number(e.target.value) }))}
                        className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                      />
                    </div>

                    <div className="flex items-center">
                      <label className="flex items-center space-x-2 space-x-reverse">
                        <input
                          type="checkbox"
                          checked={bannerForm.active || false}
                          onChange={(e) => setBannerForm((prev) => ({ ...prev, active: e.target.checked }))}
                          className="rounded"
                        />
                        <span>نشط (يظهر في الصفحة الرئيسية)</span>
                      </label>
                    </div>
                  </div>
                </>
              ) : (
                /* Section Form */
                <>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium mb-2">اسم القسم</label>
                      <input
                        type="text"
                        value={sectionForm.title || ""}
                        onChange={(e) => setSectionForm((prev) => ({ ...prev, title: e.target.value }))}
                        className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                        placeholder="مثل: الألعاب الشائعة"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">الرمز التعبيري</label>
                      <input
                        type="text"
                        value={sectionForm.emoji || ""}
                        onChange={(e) => setSectionForm((prev) => ({ ...prev, emoji: e.target.value }))}
                        className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                        placeholder="🔥"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-4">اختيار المنتجات</label>
                    <div className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50 max-h-64 overflow-y-auto">
                      <div className="grid grid-cols-1 gap-3">
                        {products.map((product) => (
                          <label
                            key={product.id}
                            className="flex items-center space-x-3 space-x-reverse p-3 rounded-lg hover:bg-gray-600/30 transition-colors cursor-pointer"
                          >
                            <input
                              type="checkbox"
                              checked={(sectionForm.productIds || []).includes(product.id)}
                              onChange={(e) => handleProductSelection(product.id, e.target.checked)}
                              className="rounded"
                            />
                            <img
                              src={product.coverImage || "/logo.jpg"}
                              alt={product.title}
                              className="w-12 h-12 rounded-lg object-cover"
                            />
                            <div className="flex-1">
                              <h4 className="font-semibold">{product.title}</h4>
                              <p className="text-sm text-gray-400">{product.category}</p>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>
                    <p className="text-sm text-gray-400 mt-2">تم اختيار {(sectionForm.productIds || []).length} منتج</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2">ترتيب القسم</label>
                      <input
                        type="number"
                        min="1"
                        value={sectionForm.order || 1}
                        onChange={(e) => setSectionForm((prev) => ({ ...prev, order: Number(e.target.value) }))}
                        className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                      />
                    </div>

                    <div className="flex items-center">
                      <label className="flex items-center space-x-2 space-x-reverse">
                        <input
                          type="checkbox"
                          checked={sectionForm.active || false}
                          onChange={(e) => setSectionForm((prev) => ({ ...prev, active: e.target.checked }))}
                          className="rounded"
                        />
                        <span>نشط (يظهر في الصفحة الرئيسية)</span>
                      </label>
                    </div>
                  </div>
                </>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-4 space-x-reverse pt-6 border-t border-gray-700/50">
                <button
                  onClick={handleSave}
                  disabled={isLoading}
                  className="flex-1 btn-primary flex items-center justify-center space-x-2 space-x-reverse"
                >
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  ) : (
                    <>
                      <Save className="w-5 h-5" />
                      <span>{editingItem ? "تحديث" : "إضافة"}</span>
                    </>
                  )}
                </button>
                <button onClick={closeModal} disabled={isLoading} className="flex-1 btn-secondary">
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
